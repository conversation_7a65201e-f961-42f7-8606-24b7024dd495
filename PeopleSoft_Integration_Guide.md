# PeopleSoft Integration Guide for Course Approval System

## Overview
This guide outlines how to integrate Oracle PeopleSoft with SharePoint for automated course approval data synchronization.

## Recommended Architecture

```
PeopleSoft ERP → Scheduled Export → Power Automate → SharePoint List → Course Advisors
```

## Phase 1: PeopleSoft Configuration

### 1. Create Data Query in PeopleSoft
Work with your PeopleSoft administrator to create a query that extracts:

```sql
-- Sample PeopleSoft Query Structure
SELECT 
    STUDENT.EMPLID,
    STUDENT.FIRST_NAME,
    STUDENT.LAST_NAME,
    PROGRAM.ACAD_PROG as Programme,
    ADVISOR.EMAIL_ADDR as Supervisor_Email,
    APPROVAL.COURSE_LIST,
    APPROVAL.STATUS,
    APPROVAL.STATUS_DATE,
    APPROVAL.INTAKE_TERM,
    APPROVAL.CITIZENSHIP,
    APPROVAL.ACAD_CAREER
FROM PS_PERSONAL_DATA STUDENT
JOIN PS_ACAD_PROG PROGRAM ON STUDENT.EMPLID = PROGRAM.EMPLID
JOIN PS_COURSE_APPROVAL APPROVAL ON STUDENT.EMPLID = APPROVAL.EMPLID
JOIN PS_EMAIL_ADDRESSES ADVISOR ON APPROVAL.SUPERVISOR_ID = ADVISOR.EMPLID
WHERE APPROVAL.STATUS IN ('PENDING', 'SUBMITTED')
AND APPROVAL.SUPERVISOR_ID IS NOT NULL
ORDER BY APPROVAL.STATUS_DATE DESC
```

### 2. Schedule Automated Export
Configure PeopleSoft to export this data:
- **Frequency**: Daily at 6:00 AM
- **Format**: CSV with headers
- **Location**: Shared network drive accessible to Power Automate
- **Filename**: `CourseApprovals_YYYYMMDD_HHMMSS.csv`

### 3. Export File Structure
Ensure the CSV contains these columns:
```
EMPLID,FIRST_NAME,LAST_NAME,Programme,Supervisor_Email,Course_List,Status,Status_Date,Intake_Term,Citizenship,Acad_Career
```

## Phase 2: Power Automate Integration

### 1. Create Power Automate Flow

**Flow Name**: "PeopleSoft Course Approval Sync"

**Trigger**: Recurrence (Daily at 6:30 AM)

### 2. Flow Steps Configuration

**Step 1: Check for New Files**
```
Action: List files in folder
Folder: \\shared\course_approvals\
Filter: Name contains "CourseApprovals_" AND Created today
```

**Step 2: Process Each File**
```
Action: For each file found
  - Get file content
  - Parse CSV to JSON
  - Process each row
```

**Step 3: Update SharePoint List**
```
For each student record:
  - Check if EMPLID exists in SharePoint list
  - If exists: Update record
  - If new: Create new record
  - Map PeopleSoft fields to SharePoint columns
```

**Step 4: Error Handling**
```
- Log any processing errors
- Send email notification to admin if errors occur
- Move processed file to archive folder
```

### 3. Field Mapping Configuration

| PeopleSoft Field | SharePoint Column | Data Type |
|------------------|-------------------|-----------|
| EMPLID | Student_ID | Number |
| FIRST_NAME | First_Name | Text |
| LAST_NAME | Last_Name | Text |
| Programme | Programme | Text |
| Supervisor_Email | Supervisor | Person/Group |
| Course_List | Courses_Selected | Multi-line Text |
| Status | Item_Status | Choice |
| Status_Date | Status_Date | Date/Time |
| Intake_Term | Intake | Text |
| Citizenship | Citizenship | Text |
| Acad_Career | Acad_Career | Text |

## Phase 3: SharePoint Configuration

### 1. List Column Setup
```
- Student_ID (Number, Indexed)
- First_Name (Single line text)
- Last_Name (Single line text)
- Programme (Single line text)
- Supervisor (Person/Group, Required)
- Courses_Selected (Multiple lines text)
- Item_Status (Choice: Pending, Approved, Rejected)
- Status_Date (Date and time)
- Intake (Single line text)
- Citizenship (Single line text)
- Acad_Career (Single line text)
- Last_Updated (Date and time, Auto-populated)
```

### 2. Create Views
```
View 1: "My Pending Students"
- Filter: Supervisor = [Me] AND Item_Status = "Pending"
- Sort: Status_Date (Descending)

View 2: "My All Students"  
- Filter: Supervisor = [Me]
- Sort: Status_Date (Descending)

View 3: "Recently Updated"
- Filter: Supervisor = [Me] AND Last_Updated >= Today-7
- Sort: Last_Updated (Descending)
```

## Phase 4: Data Synchronization Strategy

### 1. Handling Updates
```
Power Automate Logic:
IF EMPLID exists in SharePoint:
  - Compare Status_Date from PeopleSoft vs SharePoint
  - If PeopleSoft is newer: Update SharePoint record
  - If same: Skip (no changes)
ELSE:
  - Create new SharePoint record
```

### 2. Handling Deletions
```
Weekly Cleanup Process:
- Identify records in SharePoint not in latest PeopleSoft export
- Mark as "Completed" or "Withdrawn" 
- Archive old records (older than 6 months)
```

### 3. Data Validation
```
Validation Rules:
- EMPLID must be numeric and unique
- Supervisor_Email must be valid email format
- Status must be valid choice value
- Status_Date must be valid date
```

## Phase 5: Monitoring & Maintenance

### 1. Success Monitoring
```
Daily Checks:
- Verify Power Automate flow ran successfully
- Check SharePoint list for new/updated records
- Monitor for any error notifications
```

### 2. Error Handling
```
Common Issues & Solutions:
- File not found: Check PeopleSoft export schedule
- Permission errors: Verify service account access
- Data format errors: Validate CSV structure
- Supervisor not found: Check email mapping
```

### 3. Performance Optimization
```
Best Practices:
- Index Student_ID column for faster lookups
- Limit SharePoint list views to essential columns
- Archive old records regularly
- Monitor Power Automate execution time
```

## Implementation Timeline

**Week 1: PeopleSoft Setup**
- Day 1-2: Work with IT to create query
- Day 3-4: Set up scheduled export
- Day 5: Test export file format

**Week 2: Power Automate Development**
- Day 1-2: Create and configure flow
- Day 3-4: Test with sample data
- Day 5: Implement error handling

**Week 3: SharePoint Configuration**
- Day 1-2: Set up list and columns
- Day 3-4: Create views and permissions
- Day 5: End-to-end testing

**Week 4: Deployment & Training**
- Day 1-2: Deploy to production
- Day 3-4: Train course advisors
- Day 5: Monitor and refine

## Security Considerations

### 1. Data Access
- Use service account for PeopleSoft export
- Limit SharePoint access to authorized users only
- Encrypt data in transit and at rest

### 2. Audit Trail
- Log all data synchronization activities
- Track who accesses student data
- Maintain change history in SharePoint

### 3. Compliance
- Ensure FERPA compliance for student data
- Follow institutional data governance policies
- Regular security reviews and updates

## Success Metrics

### 1. Efficiency Gains
- Reduce manual email distribution time
- Eliminate Excel file management overhead
- Faster access to current student data

### 2. Data Quality
- Real-time synchronization with PeopleSoft
- Reduced data entry errors
- Consistent data across systems

### 3. User Satisfaction
- Course advisor feedback on usability
- Reduced support requests
- Improved workflow efficiency
