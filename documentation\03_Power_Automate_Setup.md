# Power Automate Setup Guide

## 📋 Overview

This document provides detailed instructions for creating and configuring the Power Automate flow that synchronizes data between PeopleSoft exports and the SharePoint Course Approval list.

## 🏗️ Flow Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                         Power Automate Flow Structure                          │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌──────────────┐    ┌─────────────┐    ┌─────────────────┐ │
│  │   Trigger   │───▶│   Get File   │───▶│ Parse Data  │───▶│ Update SharePoint│ │
│  │ (Daily 6:30)│    │   Content    │    │   (CSV)     │    │      List       │ │
│  └─────────────┘    └──────────────┘    └─────────────┘    └─────────────────┘ │
│                                                ▲                                │
│  ┌─────────────┐    ┌──────────────┐    ┌─────────────┐    ┌─────────────────┐ │
│  │ Error       │◄───│   Archive    │◄───│ Validation  │◄───│   Notification  │ │
│  │ Handling    │    │    File      │    │   Checks    │    │   (Success)     │ │
│  └─────────────┘    └──────────────┘    └─────────────┘    └─────────────────┘ │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 🔧 Flow Creation Steps

### Step 1: Create New Flow

1. **Access Power Automate**
   - Go to https://make.powerautomate.com
   - Sign in with your institutional account
   - Ensure you're in the correct environment

2. **Create Flow**
   - Click "Create" → "Scheduled cloud flow"
   - Flow name: "PeopleSoft Course Approval Sync"
   - Starting: Tomorrow at 6:30 AM
   - Repeat every: 1 Day
   - Click "Create"

### Step 2: Configure Trigger

**Recurrence Trigger Settings:**
```json
{
  "recurrence": {
    "frequency": "Day",
    "interval": 1,
    "schedule": {
      "hours": ["6"],
      "minutes": [30]
    },
    "timeZone": "New Zealand Standard Time"
  }
}
```

**Advanced Settings:**
- Start time: 6:30 AM
- Time zone: New Zealand Standard Time
- On these days: Monday, Tuesday, Wednesday, Thursday, Friday

### Step 3: Add File Detection

**Action: List files in folder**
1. Click "New step"
2. Search for "SharePoint"
3. Choose "List files in folder"
4. Configure:
   - Site Address: [Your SharePoint site]
   - Folder Id: `/shared/course_approvals/exports`
   - Include nested folders: No

**Filter Files (Add Condition)**
1. Click "New step" → "Condition"
2. Choose value: `name` (from dynamic content)
3. Condition: `contains`
4. Value: `CourseApprovals_`
5. Add second condition (AND):
   - Choose value: `name`
   - Condition: `contains`
   - Value: `@{formatDateTime(utcNow(), 'yyyyMMdd')}`

### Step 4: Process File Content

**Action: Get file content**
1. Add action inside "Yes" branch of condition
2. Search for "SharePoint"
3. Choose "Get file content"
4. Configure:
   - Site Address: [Your SharePoint site]
   - File Identifier: `id` (from List files dynamic content)

**Action: Parse CSV**
1. Click "New step"
2. Search for "Data Operation"
3. Choose "Parse JSON"
4. Configure:
   - Content: `body` (from Get file content)
   - Schema: [See CSV Schema below]

### Step 5: CSV Schema Definition

```json
{
  "type": "array",
  "items": {
    "type": "object",
    "properties": {
      "EMPLID": {"type": "string"},
      "FIRST_NAME": {"type": "string"},
      "LAST_NAME": {"type": "string"},
      "Programme": {"type": "string"},
      "Supervisor_Email": {"type": "string"},
      "Courses_Selected": {"type": "string"},
      "Item_Status": {"type": "string"},
      "STATUS_DATE": {"type": "string"},
      "Intake": {"type": "string"},
      "CITIZENSHIP": {"type": "string"},
      "ACAD_CAREER": {"type": "string"}
    },
    "required": [
      "EMPLID",
      "FIRST_NAME",
      "LAST_NAME",
      "Supervisor_Email",
      "Item_Status"
    ]
  }
}
```

### Step 6: Process Each Record

**Action: Apply to each**
1. Click "New step"
2. Choose "Apply to each"
3. Select output: `body` (from Parse JSON)

**Inside Apply to each - Check if record exists:**
1. Add action: "Get items" (SharePoint)
2. Configure:
   - Site Address: [Your SharePoint site]
   - List Name: Course Approval Pending
   - Filter Query: `Student_ID eq '@{items('Apply_to_each')?['EMPLID']}'`
   - Top Count: 1

### Step 7: Update or Create Records

**Add Condition: Record exists?**
1. Add "Condition" inside Apply to each
2. Choose value: `length(body('Get_items')?['value'])`
3. Condition: `is greater than`
4. Value: `0`

**Yes Branch - Update existing record:**
```json
{
  "action": "Update item",
  "site": "[SharePoint Site]",
  "list": "Course Approval Pending",
  "id": "@{first(body('Get_items')?['value'])?['ID']}",
  "fields": {
    "First_Name": "@{items('Apply_to_each')?['FIRST_NAME']}",
    "Last_Name": "@{items('Apply_to_each')?['LAST_NAME']}",
    "Programme": "@{items('Apply_to_each')?['Programme']}",
    "SupervisorLookupId": "@{variables('SupervisorId')}",
    "Courses_Selected": "@{items('Apply_to_each')?['Courses_Selected']}",
    "Item_Status": "@{items('Apply_to_each')?['Item_Status']}",
    "Status_Date": "@{items('Apply_to_each')?['STATUS_DATE']}",
    "Intake": "@{items('Apply_to_each')?['Intake']}",
    "Citizenship": "@{items('Apply_to_each')?['CITIZENSHIP']}",
    "Acad_Career": "@{items('Apply_to_each')?['ACAD_CAREER']}"
  }
}
```

**No Branch - Create new record:**
```json
{
  "action": "Create item",
  "site": "[SharePoint Site]",
  "list": "Course Approval Pending",
  "fields": {
    "Student_ID": "@{items('Apply_to_each')?['EMPLID']}",
    "First_Name": "@{items('Apply_to_each')?['FIRST_NAME']}",
    "Last_Name": "@{items('Apply_to_each')?['LAST_NAME']}",
    "Programme": "@{items('Apply_to_each')?['Programme']}",
    "SupervisorLookupId": "@{variables('SupervisorId')}",
    "Courses_Selected": "@{items('Apply_to_each')?['Courses_Selected']}",
    "Item_Status": "@{items('Apply_to_each')?['Item_Status']}",
    "Status_Date": "@{items('Apply_to_each')?['STATUS_DATE']}",
    "Intake": "@{items('Apply_to_each')?['Intake']}",
    "Citizenship": "@{items('Apply_to_each')?['CITIZENSHIP']}",
    "Acad_Career": "@{items('Apply_to_each')?['ACAD_CAREER']}"
  }
}
```

### Step 8: Supervisor Lookup Logic

**Before the Apply to each loop, add:**

**Action: Initialize variable**
1. Name: `SupervisorId`
2. Type: Integer
3. Value: `0`

**Inside Apply to each, before the condition:**

**Action: Get user profile**
1. Search for "Office 365 Users"
2. Choose "Get user profile (V2)"
3. User (UPN): `@{items('Apply_to_each')?['Supervisor_Email']}`

**Action: Set variable**
1. Name: `SupervisorId`
2. Value: `@{body('Get_user_profile_(V2)')?['Id']}`

### Step 9: Error Handling

**Add Parallel Branch for Error Handling:**

**Configure run after settings:**
1. Click "..." on any action
2. Choose "Configure run after"
3. Check: "is successful", "has failed", "is skipped", "has timed out"

**Action: Send error notification**
```json
{
  "action": "Send an email (V2)",
  "to": "<EMAIL>",
  "subject": "Course Approval Sync - Error",
  "body": "The daily course approval sync failed.\n\nError: @{result('Apply_to_each')}\n\nTime: @{utcNow()}\n\nPlease investigate immediately."
}
```

### Step 10: Success Notification

**After Apply to each completes successfully:**

**Action: Send success notification**
```json
{
  "action": "Send an email (V2)",
  "to": "<EMAIL>",
  "subject": "Course Approval Sync - Success",
  "body": "Daily course approval sync completed successfully.\n\nRecords processed: @{length(body('Parse_JSON'))}\n\nTime: @{utcNow()}\n\nFile: @{first(body('List_files_in_folder')?['value'])?['name']}"
}
```

### Step 11: File Archival

**Action: Copy file**
1. Add action: "Copy file" (SharePoint)
2. Configure:
   - Site Address: [Your SharePoint site]
   - Source File: `@{first(body('List_files_in_folder')?['value'])?['id']}`
   - Destination Site: [Same SharePoint site]
   - Destination Folder: `/shared/course_approvals/archive`
   - Overwrite: Yes

**Action: Delete file**
1. Add action: "Delete file" (SharePoint)
2. Configure:
   - Site Address: [Your SharePoint site]
   - File Identifier: `@{first(body('List_files_in_folder')?['value'])?['id']}`

## 🔍 Advanced Configuration

### Data Validation

**Add validation before processing:**
```json
{
  "condition": "and",
  "operands": [
    {
      "condition": "not",
      "operand": {
        "condition": "equals",
        "left": "@{items('Apply_to_each')?['EMPLID']}",
        "right": ""
      }
    },
    {
      "condition": "not",
      "operand": {
        "condition": "equals",
        "left": "@{items('Apply_to_each')?['Supervisor_Email']}",
        "right": ""
      }
    },
    {
      "condition": "contains",
      "left": "@{items('Apply_to_each')?['Supervisor_Email']}",
      "right": "@"
    }
  ]
}
```

### Performance Optimization

**Batch Processing (if needed):**
1. Use "Batch" action for large datasets
2. Process in chunks of 100 records
3. Add delays between batches if needed

**Parallel Processing:**
1. Split large files into smaller chunks
2. Process multiple chunks in parallel
3. Combine results at the end

### Monitoring and Logging

**Add logging actions:**
```json
{
  "action": "Compose",
  "inputs": {
    "timestamp": "@{utcNow()}",
    "action": "Processing record",
    "student_id": "@{items('Apply_to_each')?['EMPLID']}",
    "supervisor": "@{items('Apply_to_each')?['Supervisor_Email']}",
    "status": "@{items('Apply_to_each')?['Item_Status']}"
  }
}
```

## ✅ Testing Procedures

### Unit Testing

**Test with sample data:**
1. Create test CSV file with 5-10 records
2. Upload to export folder
3. Manually trigger flow
4. Verify records are created/updated correctly
5. Check error handling with invalid data

### Integration Testing

**End-to-end testing:**
1. Use actual PeopleSoft export file
2. Run complete flow
3. Verify all records processed
4. Check SharePoint list accuracy
5. Validate personal views work correctly

### Performance Testing

**Load testing:**
1. Test with full dataset (379+ records)
2. Monitor execution time
3. Check for timeout issues
4. Verify memory usage
5. Test concurrent access

## 🚨 Troubleshooting

### Common Issues

**Issue: Flow fails to find files**
```
Cause: File path or naming convention incorrect
Solution:
1. Verify file location path
2. Check filename pattern matching
3. Ensure proper permissions on file location
4. Test with manual file placement
```

**Issue: CSV parsing errors**
```
Cause: File format or encoding issues
Solution:
1. Verify CSV format matches schema
2. Check for special characters
3. Ensure UTF-8 encoding
4. Validate required fields are present
```

**Issue: SharePoint update failures**
```
Cause: Permission or field mapping issues
Solution:
1. Check SharePoint list permissions
2. Verify column names match exactly
3. Validate data types
4. Check for required field violations
```

**Issue: Supervisor lookup failures**
```
Cause: Email not found in directory
Solution:
1. Verify email addresses are correct
2. Check Azure AD user existence
3. Implement fallback logic
4. Add error handling for missing users
```

### Debugging Steps

1. **Enable flow analytics**
2. **Add logging at each step**
3. **Test with small datasets first**
4. **Use flow checker for validation**
5. **Monitor run history for patterns**

## 📊 Monitoring and Maintenance

### Daily Monitoring

**Check these items daily:**
- [ ] Flow execution status
- [ ] Number of records processed
- [ ] Error notifications received
- [ ] SharePoint list updated correctly

### Weekly Maintenance

**Perform these tasks weekly:**
- [ ] Review flow run history
- [ ] Check for performance degradation
- [ ] Validate data accuracy
- [ ] Clean up archived files

### Monthly Review

**Monthly maintenance tasks:**
- [ ] Review and optimize flow performance
- [ ] Update error handling as needed
- [ ] Check for new requirements
- [ ] Update documentation

## 📞 Support Information

### Power Automate Support
- **Power Platform Admin**: [Contact Information]
- **Flow Developer**: [Contact Information]
- **Technical Support**: [Contact Information]

### Escalation Procedures
1. **Level 1**: Check flow run history and common issues
2. **Level 2**: Contact Power Platform administrator
3. **Level 3**: Engage Microsoft support if needed

---

**Document Version**: 1.0  
**Last Updated**: June 19, 2025  
**Next Review**: July 19, 2025
