# Power Apps Formulas for Course Approval App

## Screen: MainScreen

### OnVisible Property
```powerquery
// Refresh data when screen loads
Refresh('Course Approval Pending');
Set(varCurrentUser, User().Email);
Set(varPendingCount, CountRows(Filter('Course Approval Pending', Supervisor.Email = varCurrentUser && Item_Status = "Pending")))
```

## Control: lblWelcome (Label)

### Text Property
```powerquery
"Welcome, " & User().FullName
```

## Control: lblPendingCount (Label)

### Text Property
```powerquery
"Pending Approvals: " & varPendingCount
```

## Control: txtSearch (Text Input)

### HintText Property
```powerquery
"Search by student name, programme, or courses..."
```

### Default Property
```powerquery
""
```

## Control: ddFilter (Dropdown)

### Items Property
```powerquery
["All", "Pending", "Approved", "Rejected"]
```

### Default Property
```powerquery
"All"
```

## Control: galStudents (Gallery)

### Items Property
```powerquery
If(
    ddFilter.Selected.Value = "All",
    Filter(
        'Course Approval Pending',
        Supervisor.Email = varCurrentUser &&
        (
            IsBlank(txtSearch.Text) ||
            txtSearch.Text in First_Name ||
            txtSearch.Text in Last_Name ||
            txtSearch.Text in Programme ||
            txtSearch.Text in Courses_Selected
        )
    ),
    Filter(
        'Course Approval Pending',
        Supervisor.Email = varCurrentUser &&
        Item_Status = ddFilter.Selected.Value &&
        (
            IsBlank(txtSearch.Text) ||
            txtSearch.Text in First_Name ||
            txtSearch.Text in Last_Name ||
            txtSearch.Text in Programme ||
            txtSearch.Text in Courses_Selected
        )
    )
)
```

## Gallery Template Controls

### lblStudentName (Label in Gallery)

#### Text Property
```powerquery
ThisItem.First_Name & " " & ThisItem.Last_Name
```

#### Font Property
```powerquery
Font.'Segoe UI Semibold'
```

#### Size Property
```powerquery
14
```

### lblProgramme (Label in Gallery)

#### Text Property
```powerquery
"Programme: " & ThisItem.Programme
```

#### Color Property
```powerquery
RGBA(68, 68, 68, 1)
```

### lblCourses (Label in Gallery)

#### Text Property
```powerquery
"Courses: " & ThisItem.Courses_Selected
```

#### Color Property
```powerquery
RGBA(68, 68, 68, 1)
```

### lblStatus (Label in Gallery)

#### Text Property
```powerquery
"Status: " & ThisItem.Item_Status & " | " & Text(ThisItem.Status_Date, "mm/dd/yyyy")
```

#### Color Property
```powerquery
If(
    ThisItem.Item_Status = "Pending",
    RGBA(255, 140, 0, 1),  // Orange for pending
    If(
        ThisItem.Item_Status = "Approved",
        RGBA(0, 128, 0, 1),  // Green for approved
        RGBA(255, 0, 0, 1)   // Red for rejected
    )
)
```

### lblCourseCount (Label in Gallery)

#### Text Property
```powerquery
ThisItem.Number_of_Courses & " course" & If(ThisItem.Number_of_Courses > 1, "s", "")
```

## Control: btnRefresh (Button)

### Text Property
```powerquery
"🔄 Refresh"
```

### OnSelect Property
```powerquery
Refresh('Course Approval Pending');
Set(varPendingCount, CountRows(Filter('Course Approval Pending', Supervisor.Email = varCurrentUser && Item_Status = "Pending")));
Notify("Data refreshed successfully", NotificationType.Success)
```

## Control: btnClearSearch (Button)

### Text Property
```powerquery
"✖ Clear"
```

### OnSelect Property
```powerquery
Reset(txtSearch);
Reset(ddFilter)
```

### Visible Property
```powerquery
!IsBlank(txtSearch.Text) || ddFilter.Selected.Value <> "All"
```

## Gallery OnSelect (if you want detail view)

### OnSelect Property
```powerquery
// Store selected student for detail view
Set(varSelectedStudent, ThisItem);
// Navigate to detail screen (if you create one)
// Navigate(StudentDetailScreen, ScreenTransition.Slide)
```

## Additional Formulas for Enhanced Features

### Sort Options (if you add sorting)
```powerquery
// Sort by Last Name
SortByColumns(galStudents.Items, "Last_Name", Ascending)

// Sort by Status Date
SortByColumns(galStudents.Items, "Status_Date", Descending)

// Sort by Programme
SortByColumns(galStudents.Items, "Programme", Ascending)
```

### Error Handling
```powerquery
// Check if user has access
If(
    IsEmpty(Filter('Course Approval Pending', Supervisor.Email = User().Email)),
    Notify("No students assigned to you", NotificationType.Warning),
    // Normal gallery items formula here
)
```

### Performance Optimization
```powerquery
// Use delegation-friendly formulas
Filter(
    'Course Approval Pending',
    StartsWith(Supervisor.Email, User().Email)
)
```

## Color Scheme Suggestions

### Primary Colors
- Header Background: RGBA(0, 120, 212, 1)  // Microsoft Blue
- Text Primary: RGBA(50, 49, 48, 1)        // Dark Gray
- Text Secondary: RGBA(96, 94, 92, 1)      // Medium Gray
- Background: RGBA(250, 249, 248, 1)       // Light Gray

### Status Colors
- Pending: RGBA(255, 140, 0, 1)    // Orange
- Approved: RGBA(16, 124, 16, 1)   // Green
- Rejected: RGBA(196, 43, 28, 1)   // Red

## Screen Size Settings

### Tablet Layout
- Width: 1366
- Height: 768
- Orientation: Landscape

### Mobile Responsive (if needed)
- Use responsive containers
- Adjust font sizes based on screen size
- Stack elements vertically on small screens
```

This configuration will create a professional, functional Power Apps solution that meets your requirements perfectly!
