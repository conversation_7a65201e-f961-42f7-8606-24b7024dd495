# PeopleSoft Integration Guide

## 📋 Overview

This document provides detailed instructions for integrating Oracle PeopleSoft with the Course Advisor Automation System. The integration uses scheduled data exports from PeopleSoft to maintain current student course approval information.

## 🏗️ Integration Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           PeopleSoft Integration Flow                          │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────┐    ┌──────────────┐    ┌─────────────┐    ┌─────────────────┐ │
│  │ PeopleSoft  │───▶│   Scheduled  │───▶│   Shared    │───▶│ Power Automate  │ │
│  │   Query     │    │   Export     │    │   File      │    │     Flow        │ │
│  │             │    │  (Daily 6AM) │    │  Location   │    │  (Daily 6:30AM)│ │
│  └─────────────┘    └──────────────┘    └─────────────┘    └─────────────────┘ │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 📊 Data Requirements

### Source Tables in PeopleSoft
The integration requires access to the following PeopleSoft tables:

| Table | Purpose | Key Fields |
|-------|---------|------------|
| PS_PERSONAL_DATA | Student personal information | EMPLID, FIRST_NAME, LAST_NAME |
| PS_ACAD_PROG | Academic programme data | EMPLID, ACAD_PROG |
| PS_COURSE_APPROVAL | Course approval records | EMPLID, STATUS, STATUS_DATE, SUPERVISOR_ID |
| PS_EMAIL_ADDRESSES | Contact information | EMPLID, EMAIL_ADDR |
| PS_COURSE_SELECTION | Selected courses | EMPLID, COURSE_LIST |

### Required Data Fields
The export must include these fields for proper integration:

```sql
-- Required fields for SharePoint integration
EMPLID              -- Student ID (Primary Key)
FIRST_NAME          -- Student first name
LAST_NAME           -- Student last name
ACAD_PROG           -- Academic programme
SUPERVISOR_EMAIL    -- Course advisor email
COURSE_LIST         -- Comma-separated course codes
STATUS              -- Approval status (PENDING, APPROVED, REJECTED)
STATUS_DATE         -- Last status update date
INTAKE_TERM         -- Academic intake period
CITIZENSHIP         -- Student citizenship
ACAD_CAREER         -- Academic career level
```

## 🔧 PeopleSoft Query Configuration

### Step 1: Create the Data Query

**Query Name**: `COURSE_APPROVAL_EXPORT`

**SQL Query**:
```sql
SELECT DISTINCT
    s.EMPLID,
    s.FIRST_NAME,
    s.LAST_NAME,
    p.ACAD_PROG as Programme,
    e.EMAIL_ADDR as Supervisor_Email,
    c.COURSE_LIST as Courses_Selected,
    a.STATUS as Item_Status,
    a.STATUS_DATE,
    a.INTAKE_TERM as Intake,
    s.CITIZENSHIP,
    p.ACAD_CAREER
FROM PS_PERSONAL_DATA s
    INNER JOIN PS_ACAD_PROG p ON s.EMPLID = p.EMPLID
    INNER JOIN PS_COURSE_APPROVAL a ON s.EMPLID = a.EMPLID
    INNER JOIN PS_EMAIL_ADDRESSES e ON a.SUPERVISOR_ID = e.EMPLID
    LEFT JOIN PS_COURSE_SELECTION c ON s.EMPLID = c.EMPLID
WHERE a.STATUS IN ('PENDING', 'SUBMITTED', 'APPROVED')
    AND a.SUPERVISOR_ID IS NOT NULL
    AND e.EMAIL_ADDR IS NOT NULL
    AND p.ACAD_PROG IS NOT NULL
ORDER BY a.STATUS_DATE DESC, s.LAST_NAME, s.FIRST_NAME
```

### Step 2: Query Validation

**Test the query with sample data**:
```sql
-- Test query to verify data quality
SELECT 
    COUNT(*) as Total_Records,
    COUNT(DISTINCT EMPLID) as Unique_Students,
    COUNT(DISTINCT Supervisor_Email) as Unique_Advisors,
    MIN(STATUS_DATE) as Earliest_Date,
    MAX(STATUS_DATE) as Latest_Date
FROM (
    -- Insert the main query here
) validation_query;
```

**Expected Results**:
- Total Records: ~379 (based on current data)
- Unique Students: Should match total records (1:1 ratio)
- Unique Advisors: ~43 (based on current data)
- Date Range: Should cover current academic period

### Step 3: Query Performance Optimization

**Add indexes if needed**:
```sql
-- Recommended indexes for performance
CREATE INDEX IDX_COURSE_APPROVAL_STATUS ON PS_COURSE_APPROVAL(STATUS, STATUS_DATE);
CREATE INDEX IDX_COURSE_APPROVAL_SUPERVISOR ON PS_COURSE_APPROVAL(SUPERVISOR_ID);
CREATE INDEX IDX_EMAIL_ADDRESSES_EMPLID ON PS_EMAIL_ADDRESSES(EMPLID);
```

## 📅 Scheduled Export Configuration

### Step 1: Export Job Setup

**Job Name**: `COURSE_APPROVAL_DAILY_EXPORT`

**Schedule Configuration**:
- **Frequency**: Daily
- **Time**: 6:00 AM (before business hours)
- **Days**: Monday through Friday
- **Timezone**: Local institutional timezone

### Step 2: Export Format Settings

**File Format**: CSV with headers
**Character Encoding**: UTF-8
**Field Delimiter**: Comma (,)
**Text Qualifier**: Double quotes (")
**Date Format**: YYYY-MM-DD
**Null Value Handling**: Empty string

**Sample Export Header**:
```csv
EMPLID,FIRST_NAME,LAST_NAME,Programme,Supervisor_Email,Courses_Selected,Item_Status,STATUS_DATE,Intake,CITIZENSHIP,ACAD_CAREER
```

### Step 3: File Naming Convention

**Filename Pattern**: `CourseApprovals_YYYYMMDD_HHMMSS.csv`

**Examples**:
- `CourseApprovals_20250619_060000.csv`
- `CourseApprovals_20250620_060000.csv`

### Step 4: Export Location

**Primary Location**: `\\shared\course_approvals\exports\`
**Backup Location**: `\\shared\course_approvals\backup\`
**Archive Location**: `\\shared\course_approvals\archive\`

**Folder Structure**:
```
\\shared\course_approvals\
├── exports\          (Current day's export)
├── backup\           (Previous 7 days)
├── archive\          (Older than 7 days)
└── logs\             (Export job logs)
```

## 🔒 Security Configuration

### Step 1: Service Account Setup

**Account Name**: `svc_courseapproval`
**Purpose**: Dedicated service account for data export
**Permissions Required**:
- Read access to required PeopleSoft tables
- Write access to shared file location
- Execute permissions for scheduled jobs

### Step 2: Data Access Controls

**PeopleSoft Security**:
```sql
-- Grant minimum required permissions
GRANT SELECT ON PS_PERSONAL_DATA TO svc_courseapproval;
GRANT SELECT ON PS_ACAD_PROG TO svc_courseapproval;
GRANT SELECT ON PS_COURSE_APPROVAL TO svc_courseapproval;
GRANT SELECT ON PS_EMAIL_ADDRESSES TO svc_courseapproval;
GRANT SELECT ON PS_COURSE_SELECTION TO svc_courseapproval;
```

**File System Security**:
- Service account: Read/Write access to export folders
- Power Automate service: Read access to export folder
- Administrators: Full control
- Other users: No access

### Step 3: Data Privacy Compliance

**FERPA Compliance**:
- Limit data export to essential fields only
- Implement audit logging for all data access
- Ensure secure transmission and storage
- Regular access reviews and cleanup

**Audit Requirements**:
- Log all export job executions
- Track file access and downloads
- Monitor for unauthorized access attempts
- Maintain 90-day audit trail

## 📊 Data Quality Validation

### Step 1: Pre-Export Validation

**Data Quality Checks**:
```sql
-- Check for missing required fields
SELECT 'Missing EMPLID' as Issue, COUNT(*) as Count
FROM PS_PERSONAL_DATA WHERE EMPLID IS NULL
UNION ALL
SELECT 'Missing Supervisor Email' as Issue, COUNT(*)
FROM PS_COURSE_APPROVAL a
LEFT JOIN PS_EMAIL_ADDRESSES e ON a.SUPERVISOR_ID = e.EMPLID
WHERE a.STATUS IN ('PENDING', 'SUBMITTED') AND e.EMAIL_ADDR IS NULL
UNION ALL
SELECT 'Invalid Email Format' as Issue, COUNT(*)
FROM PS_EMAIL_ADDRESSES 
WHERE EMAIL_ADDR NOT LIKE '%@%.%';
```

### Step 2: Post-Export Validation

**File Validation Script**:
```powershell
# PowerShell script to validate export file
$csvFile = "\\shared\course_approvals\exports\CourseApprovals_$(Get-Date -Format 'yyyyMMdd')_*.csv"
$data = Import-Csv $csvFile

# Validation checks
$totalRecords = $data.Count
$uniqueStudents = ($data | Select-Object -Unique EMPLID).Count
$uniqueAdvisors = ($data | Select-Object -Unique Supervisor_Email).Count
$missingEmails = ($data | Where-Object {$_.Supervisor_Email -eq ""}).Count

Write-Output "Validation Results:"
Write-Output "Total Records: $totalRecords"
Write-Output "Unique Students: $uniqueStudents"
Write-Output "Unique Advisors: $uniqueAdvisors"
Write-Output "Missing Emails: $missingEmails"

# Alert if validation fails
if ($missingEmails -gt 0) {
    Write-Warning "Data quality issue: $missingEmails records missing supervisor email"
}
```

## 🚨 Error Handling & Monitoring

### Step 1: Export Job Monitoring

**Success Criteria**:
- Export job completes without errors
- File is created in expected location
- File contains expected number of records
- All required fields are populated

**Failure Scenarios**:
- Database connection issues
- Query execution errors
- File system access problems
- Data quality validation failures

### Step 2: Notification Setup

**Success Notification**:
```
Subject: Course Approval Export - Success
Body: Daily export completed successfully
- Records exported: [COUNT]
- File location: [PATH]
- Export time: [TIMESTAMP]
```

**Error Notification**:
```
Subject: Course Approval Export - FAILED
Body: Daily export failed - immediate attention required
- Error: [ERROR_MESSAGE]
- Time: [TIMESTAMP]
- Contact: IT Support immediately
```

### Step 3: Backup and Recovery

**Backup Strategy**:
- Retain 7 days of export files
- Archive monthly snapshots
- Maintain export job logs for 90 days

**Recovery Procedures**:
1. Identify the issue (job failure, data corruption, etc.)
2. Check recent backup files
3. Re-run export job if needed
4. Validate data integrity
5. Notify downstream systems

## ✅ Implementation Checklist

### Pre-Implementation
- [ ] Identify PeopleSoft administrator contact
- [ ] Confirm access to required tables
- [ ] Set up service account with appropriate permissions
- [ ] Create shared file location with proper security
- [ ] Test database connectivity

### Implementation
- [ ] Create and test the data query
- [ ] Set up scheduled export job
- [ ] Configure file naming and location
- [ ] Implement data validation checks
- [ ] Set up monitoring and notifications

### Post-Implementation
- [ ] Monitor first week of exports
- [ ] Validate data quality and completeness
- [ ] Confirm Power Automate integration
- [ ] Document any issues and resolutions
- [ ] Schedule regular maintenance reviews

## 📞 Support Contacts

### PeopleSoft Team
- **Database Administrator**: [Contact Information]
- **Security Administrator**: [Contact Information]
- **Application Support**: [Contact Information]

### Infrastructure Team
- **File System Administrator**: [Contact Information]
- **Network Administrator**: [Contact Information]
- **Backup Administrator**: [Contact Information]

---

**Document Version**: 1.0  
**Last Updated**: June 19, 2025  
**Next Review**: July 19, 2025
