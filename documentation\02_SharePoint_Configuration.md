# SharePoint Configuration Guide

## 📋 Overview

This document provides step-by-step instructions for configuring SharePoint to support the Course Advisor Automation System. The SharePoint list will serve as the central repository for course approval data with personalized views for each advisor.

## 🏗️ SharePoint Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                         SharePoint List Structure                              │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────────────────┐ │
│  │   Course        │───▶│   Personal      │───▶│      Course Advisors        │ │
│  │   Approval      │    │   Views         │    │     (30 users)              │ │
│  │   List          │    │ (Filter by Me)  │    │   - My Pending Students     │ │
│  │                 │    │                 │    │   - My All Students         │ │
│  └─────────────────┘    └─────────────────┘    └─────────────────────────────┘ │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 📊 List Schema Design

### Column Configuration

| Column Name | Type | Required | Settings | Purpose |
|-------------|------|----------|----------|---------|
| Student_ID | Number | Yes | Indexed, No duplicates | PeopleSoft EMPLID |
| First_Name | Single line text | Yes | 50 characters | Student first name |
| Last_Name | Single line text | Yes | 50 characters | Student last name |
| Programme | Single line text | Yes | 100 characters | Academic programme |
| Supervisor | Person or Group | Yes | People only | Course advisor |
| Courses_Selected | Multiple lines text | No | Plain text, 6 lines | Course list |
| Item_Status | Choice | Yes | Pending, Approved, Rejected | Approval status |
| Status_Date | Date and time | Yes | Date and time format | Last update |
| Intake | Single line text | No | 20 characters | Academic intake |
| Citizenship | Single line text | No | 50 characters | Student citizenship |
| Acad_Career | Single line text | No | 20 characters | Academic level |
| Last_Updated | Date and time | No | Auto-populated | System timestamp |

## 🔧 Step-by-Step Implementation

### Step 1: Create SharePoint Site (if needed)

**Option A: Use Existing Site**
1. Navigate to your existing SharePoint site
2. Ensure you have site owner permissions
3. Proceed to Step 2

**Option B: Create New Site**
1. Go to SharePoint admin center
2. Click "Create site"
3. Choose "Team site"
4. Site name: "Course Advisor Portal"
5. Description: "Course approval management system"
6. Privacy: Private (members only)
7. Add initial site owners

### Step 2: Create the Course Approval List

**Method A: Create from Scratch**
1. Go to SharePoint site
2. Click "New" → "List"
3. Choose "Blank list"
4. Name: "Course Approval Pending"
5. Description: "Student course approval tracking"
6. Click "Create"

**Method B: Import from Excel (Recommended)**
1. Go to SharePoint site
2. Click "New" → "List"
3. Choose "From Excel"
4. Upload your existing Excel file
5. Map columns appropriately
6. Click "Create"

### Step 3: Configure List Columns

**Add Required Columns:**

**Student_ID (Number)**
```
1. List Settings → Create column
2. Column name: Student_ID
3. Type: Number
4. Required: Yes
5. Enforce unique values: Yes
6. Indexed: Yes
7. Default value: (none)
8. Number of decimal places: 0
```

**First_Name (Single line text)**
```
1. List Settings → Create column
2. Column name: First_Name
3. Type: Single line of text
4. Required: Yes
5. Maximum characters: 50
6. Default value: (none)
```

**Last_Name (Single line text)**
```
1. List Settings → Create column
2. Column name: Last_Name
3. Type: Single line of text
4. Required: Yes
5. Maximum characters: 50
6. Default value: (none)
```

**Programme (Single line text)**
```
1. List Settings → Create column
2. Column name: Programme
3. Type: Single line of text
4. Required: Yes
5. Maximum characters: 100
6. Default value: (none)
```

**Supervisor (Person or Group)**
```
1. List Settings → Create column
2. Column name: Supervisor
3. Type: Person or Group
4. Required: Yes
5. Allow multiple selections: No
6. Allow selection of: People Only
7. Choose from: All Users
8. Show field: Name (with presence)
```

**Courses_Selected (Multiple lines text)**
```
1. List Settings → Create column
2. Column name: Courses_Selected
3. Type: Multiple lines of text
4. Required: No
5. Type of text: Plain text
6. Number of lines: 6
7. Append changes: No
```

**Item_Status (Choice)**
```
1. List Settings → Create column
2. Column name: Item_Status
3. Type: Choice
4. Required: Yes
5. Choices (one per line):
   - Pending
   - Approved
   - Rejected
6. Default value: Pending
7. Display choices using: Drop-Down Menu
```

**Status_Date (Date and time)**
```
1. List Settings → Create column
2. Column name: Status_Date
3. Type: Date and Time
4. Required: Yes
5. Date and Time Format: Date & Time
6. Default value: (Today)
```

**Intake (Single line text)**
```
1. List Settings → Create column
2. Column name: Intake
3. Type: Single line of text
4. Required: No
5. Maximum characters: 20
6. Default value: (none)
```

**Citizenship (Single line text)**
```
1. List Settings → Create column
2. Column name: Citizenship
3. Type: Single line of text
4. Required: No
5. Maximum characters: 50
6. Default value: (none)
```

**Acad_Career (Single line text)**
```
1. List Settings → Create column
2. Column name: Acad_Career
3. Type: Single line of text
4. Required: No
5. Maximum characters: 20
6. Default value: (none)
```

### Step 4: Configure List Settings

**General Settings**
1. Go to List Settings
2. Click "List name, description and navigation"
3. Verify settings:
   - Name: Course Approval Pending
   - Description: Student course approval tracking system
   - Navigation: Yes (display on Quick Launch)

**Advanced Settings**
1. Go to List Settings → Advanced settings
2. Configure:
   - Content types: No
   - Item-level permissions: Read items that were created by the user
   - Attachments: Disabled
   - Folders: No
   - Search: Yes
   - Offline client availability: No

**Versioning Settings**
1. Go to List Settings → Versioning settings
2. Configure:
   - Content approval: No
   - Item version history: Create major versions
   - Keep versions: 10 major versions
   - Require check out: No

### Step 5: Create Personal Views

**View 1: My Pending Students**
```
1. Go to list → All Items → Create view
2. View name: My Pending Students
3. View type: Standard View
4. Audience: Create a Personal View
5. Columns to display:
   - Student_ID
   - First_Name
   - Last_Name
   - Programme
   - Courses_Selected
   - Item_Status
   - Status_Date
6. Sort: Status_Date (descending)
7. Filter:
   - Supervisor is equal to [Me]
   - AND Item_Status is equal to Pending
8. Item Limit: 100 items
9. Mobile: Enable
10. Save
```

**View 2: My All Students**
```
1. Create view → Standard View
2. View name: My All Students
3. View type: Standard View
4. Audience: Create a Personal View
5. Columns to display:
   - Student_ID
   - First_Name
   - Last_Name
   - Programme
   - Courses_Selected
   - Item_Status
   - Status_Date
   - Intake
6. Sort: Status_Date (descending)
7. Filter:
   - Supervisor is equal to [Me]
8. Item Limit: 200 items
9. Mobile: Enable
10. Save
```

**View 3: Recently Updated**
```
1. Create view → Standard View
2. View name: Recently Updated
3. View type: Standard View
4. Audience: Create a Personal View
5. Columns to display:
   - First_Name
   - Last_Name
   - Programme
   - Item_Status
   - Status_Date
6. Sort: Status_Date (descending)
7. Filter:
   - Supervisor is equal to [Me]
   - AND Status_Date is greater than [Today]-7
8. Item Limit: 50 items
9. Mobile: Enable
10. Save
```

**Set Default View**
1. Go to List Settings → Views
2. Click "My Pending Students"
3. Check "Make this the default view"
4. Save

### Step 6: Configure Permissions

**Site-Level Permissions**
1. Go to Site Settings → Site permissions
2. Click "Invite people"
3. Add all course advisors:
   - Enter email addresses (one per line or comma-separated)
   - Set permission level: "Read" or "Contribute"
   - Include personal message
   - Send invitation

**List-Level Permissions**
1. Go to List Settings → Permissions for this list
2. If inheriting permissions, click "Stop Inheriting Permissions"
3. Verify course advisors have appropriate access:
   - Read: Can view list and items
   - Contribute: Can add, edit, and delete items
4. Remove unnecessary permissions

**Item-Level Security (Automatic)**
The personal views with "[Me]" filter automatically provide row-level security:
- Each advisor sees only records where they are the Supervisor
- No additional configuration needed
- SharePoint handles the filtering automatically

### Step 7: Customize List Appearance

**List Web Part Settings**
1. Go to list page
2. Click "Edit" (if in modern experience)
3. Configure:
   - Show command bar: Yes
   - Show filters: Yes
   - Show search: Yes
   - Compact layout: No

**Column Formatting (Optional)**
```json
// Status column formatting
{
  "$schema": "https://developer.microsoft.com/json-schemas/sp/v2/column-formatting.schema.json",
  "elmType": "div",
  "attributes": {
    "class": {
      "operator": "?",
      "operands": [
        {
          "operator": "==",
          "operands": ["@currentField", "Pending"]
        },
        "sp-field-severity--warning",
        {
          "operator": "?",
          "operands": [
            {
              "operator": "==",
              "operands": ["@currentField", "Approved"]
            },
            "sp-field-severity--good",
            "sp-field-severity--severeWarning"
          ]
        }
      ]
    }
  },
  "children": [
    {
      "elmType": "span",
      "children": [
        {
          "elmType": "span",
          "txtContent": "@currentField"
        }
      ]
    }
  ]
}
```

## 📱 Mobile Configuration

### Mobile View Optimization
1. Go to List Settings → Mobile
2. Configure mobile settings:
   - Enable mobile view: Yes
   - Mobile view name: Course Approvals
   - Mobile view URL: (auto-generated)
   - Items to display: 25
   - Simple view: Yes

### Mobile Column Priority
Set column display priority for mobile:
1. Student name: Priority 1
2. Programme: Priority 2
3. Status: Priority 3
4. Status Date: Priority 4
5. Courses: Priority 5

## 🔍 Search Configuration

### Search Settings
1. Go to List Settings → Advanced settings
2. Enable search: Yes
3. Configure search scope:
   - Include in search results: Yes
   - Index all text fields: Yes
   - Index person fields: Yes

### Search Web Part (Optional)
1. Add Search Box web part to site page
2. Configure to search within Course Approval list
3. Set result source to current list

## ✅ Testing Checklist

### Functional Testing
- [ ] Create test records with different supervisors
- [ ] Verify personal views show correct filtering
- [ ] Test search functionality
- [ ] Confirm mobile access works
- [ ] Validate permissions (users see only their records)

### Performance Testing
- [ ] Load 379+ records and test response time
- [ ] Test with multiple concurrent users
- [ ] Verify view performance with large datasets
- [ ] Check mobile performance

### User Acceptance Testing
- [ ] Have 2-3 advisors test the interface
- [ ] Gather feedback on usability
- [ ] Test with real data scenarios
- [ ] Validate business requirements

## 🚨 Troubleshooting

### Common Issues

**Issue: Personal views not filtering correctly**
```
Solution:
1. Check if [Me] filter is properly configured
2. Verify Supervisor column is Person/Group type
3. Ensure users are properly authenticated
4. Test with different user accounts
```

**Issue: Performance problems with large datasets**
```
Solution:
1. Add indexes to frequently filtered columns
2. Limit view item counts
3. Use folder structure if needed
4. Consider list threshold limits (5000 items)
```

**Issue: Mobile view not displaying properly**
```
Solution:
1. Check mobile view configuration
2. Verify column priorities are set
3. Test on different mobile devices
4. Ensure responsive design is enabled
```

## 📞 Support Information

### SharePoint Administration
- **Site Collection Administrator**: [Contact Information]
- **SharePoint Support**: [Contact Information]
- **User Training**: [Contact Information]

### Technical Support
- **IT Helpdesk**: [Contact Information]
- **Power Platform Team**: [Contact Information]

---

**Document Version**: 1.0  
**Last Updated**: June 19, 2025  
**Next Review**: July 19, 2025
