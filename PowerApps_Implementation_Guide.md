# Course Approval Power Apps Implementation Guide

## Overview
This guide will help you create a Power Apps solution to display students waiting for course approval, filtered by the signed-in course advisor.

## Data Structure (from your Excel file)
- **379 student records**
- **43 unique supervisors/advisors**
- **Key fields**: Supervisor, Student Info, Programme, Courses, Status

## Implementation Steps

### Phase 1: Data Setup (Days 1-2)

#### 1. Create SharePoint List
1. Go to your SharePoint site
2. Create new list: "Course Approval Pending"
3. Upload your Excel file to create the list structure

#### 2. Configure Columns
```
- Supervisor (Person/Group field)
- Student_ID (Number)
- First_Name (Single line text)
- Last_Name (Single line text)
- Programme (Single line text)
- Courses_Selected (Multiple lines text)
- Item_Status (Choice: Pending, Approved, Rejected)
- Status_Date (Date and Time)
- Number_of_Courses (Number)
- Academic_History (Multiple lines text)
```

### Phase 2: Power Apps Development (Days 3-5)

#### 1. Create Canvas App
1. Go to make.powerapps.com
2. Create new Canvas App (Tablet layout)
3. Connect to SharePoint list

#### 2. Main Screen Components

**Header Section:**
- App title: "Course Approval Dashboard"
- Welcome message with user name
- Pending count display

**Search Section:**
- Search text input
- Filter dropdown (by programme, status)
- Refresh button

**Student Gallery:**
- Displays filtered student list
- Shows key information per student
- Responsive design

#### 3. Key Power Apps Formulas

**Filter by Current User:**
```powerquery
Filter(
    'Course Approval Pending',
    Supervisor.Email = User().Email
)
```

**Search Functionality:**
```powerquery
Filter(
    'Course Approval Pending',
    Supervisor.Email = User().Email &&
    (
        SearchInput.Text in First_Name ||
        SearchInput.Text in Last_Name ||
        SearchInput.Text in Programme ||
        SearchInput.Text in Courses_Selected
    )
)
```

**Count Pending Approvals:**
```powerquery
CountRows(
    Filter(
        'Course Approval Pending',
        Supervisor.Email = User().Email &&
        Item_Status = "Pending"
    )
)
```

**Gallery Items (OnSelect):**
```powerquery
// Navigate to detail screen if needed
Navigate(StudentDetailScreen, ScreenTransition.Slide)
```

#### 4. Gallery Configuration
- **Template**: Title, Subtitle, Body
- **Title**: `First_Name & " " & Last_Name`
- **Subtitle**: `"Programme: " & Programme`
- **Body**: `"Courses: " & Courses_Selected & " | Status: " & Item_Status & " | Date: " & Text(Status_Date, "mm/dd/yyyy")`

### Phase 3: Authentication & Security (Days 6-7)

#### 1. Automatic Authentication
- Uses Office 365 authentication
- User().Email provides signed-in user's email
- Maps to Supervisor field for filtering

#### 2. Row-Level Security
- Each advisor sees only their assigned students
- Implemented through filter formulas
- No additional security configuration needed

### Phase 4: Testing & Deployment (Days 8-10)

#### 1. Testing Checklist
- [ ] User sees only their students
- [ ] Search works correctly
- [ ] Data refreshes properly
- [ ] Mobile responsive
- [ ] Performance acceptable

#### 2. Deployment Steps
1. Save and publish the app
2. Share with all 30 course advisors
3. Set appropriate permissions
4. Provide user training

## App Layout Design

```
┌─────────────────────────────────────────┐
│  📚 Course Approval Dashboard           │
│  Welcome, [Advisor Name]                │
├─────────────────────────────────────────┤
│  🔍 [Search Box] [Filter ▼] [Refresh]  │
│  📊 Pending Approvals: [Count]         │
├─────────────────────────────────────────┤
│  Student List:                         │
│  ┌─────────────────────────────────────┐ │
│  │ 👤 John Smith                      │ │
│  │ 📚 Programme: Business Management   │ │
│  │ 📋 Courses: BMGT 221, MGMT 103     │ │
│  │ ⏰ Status: Pending | 2025-01-15    │ │
│  └─────────────────────────────────────┘ │
│  ┌─────────────────────────────────────┐ │
│  │ 👤 Jane Doe                        │ │
│  │ 📚 Programme: Communications        │ │
│  │ 📋 Courses: COMM 112, ECON 113     │ │
│  │ ⏰ Status: Pending | 2025-01-14    │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## Next Steps

1. **Upload your Excel data to SharePoint**
2. **Create the Power Apps canvas app**
3. **Configure the formulas and layout**
4. **Test with a few advisors**
5. **Deploy to all 30 course advisors**

## Support Resources

- Power Apps documentation: docs.microsoft.com/powerapps
- SharePoint integration guide
- Your IT department for authentication setup

## Timeline: 1-2 weeks total
- Week 1: Data setup and app development
- Week 2: Testing, refinement, and deployment
