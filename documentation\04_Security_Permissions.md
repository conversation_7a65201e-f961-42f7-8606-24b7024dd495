# Security & Permissions Guide

## 📋 Overview

This document outlines the security architecture and permission configuration for the Course Advisor Automation System, ensuring proper data access controls and compliance with institutional policies.

## 🔒 Security Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           Security Layer Architecture                          │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────────────────┐ │
│  │   Azure AD      │───▶│   SharePoint    │───▶│      Row-Level Security     │ │
│  │ Authentication  │    │   Permissions   │    │    (Personal Views)         │ │
│  │                 │    │                 │    │                             │ │
│  └─────────────────┘    └─────────────────┘    └─────────────────────────────┘ │
│           │                       │                           │                │
│           ▼                       ▼                           ▼                │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────────────────┐ │
│  │ Institutional   │    │   List-Level    │    │    Data Classification     │ │
│  │    Identity     │    │   Permissions   │    │      & Compliance           │ │
│  │                 │    │                 │    │                             │ │
│  └─────────────────┘    └─────────────────┘    └─────────────────────────────┘ │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 👥 User Roles and Permissions

### Role Definitions

| Role | Count | Access Level | Permissions |
|------|-------|--------------|-------------|
| **Course Advisors** | 30 | Read/Contribute | View and update their assigned students only |
| **System Administrators** | 2-3 | Full Control | Manage system, all data access, configuration |
| **Data Managers** | 1-2 | Contribute | Manage data imports, bulk updates |
| **Read-Only Users** | 5-10 | Read | View reports, analytics (if needed) |

### Detailed Permission Matrix

| Function | Course Advisor | Data Manager | System Admin | Read-Only |
|----------|----------------|--------------|--------------|-----------|
| View own students | ✅ | ✅ | ✅ | ✅ |
| View all students | ❌ | ✅ | ✅ | ✅ |
| Update student status | ✅ | ✅ | ✅ | ❌ |
| Add new students | ❌ | ✅ | ✅ | ❌ |
| Delete students | ❌ | ✅ | ✅ | ❌ |
| Manage list structure | ❌ | ❌ | ✅ | ❌ |
| Configure permissions | ❌ | ❌ | ✅ | ❌ |
| Access audit logs | ❌ | ✅ | ✅ | ❌ |

## 🔧 SharePoint Permission Configuration

### Site-Level Permissions

**Step 1: Create Security Groups**
1. Go to Site Settings → People and groups
2. Create groups:
   - **Course Advisors** (Members)
   - **Data Managers** (Contributors)
   - **System Administrators** (Owners)
   - **Read-Only Users** (Visitors)

**Step 2: Assign Site Permissions**
```
Course Advisors Group:
- Permission Level: Read
- Can view pages and list items
- Can use remote interfaces

Data Managers Group:
- Permission Level: Contribute
- Can add, edit, and delete list items
- Can view, add, and update pages

System Administrators Group:
- Permission Level: Full Control
- Has full control over the site

Read-Only Users Group:
- Permission Level: Read
- Can view pages and list items only
```

### List-Level Permissions

**Course Approval Pending List Configuration:**

1. **Break Permission Inheritance**
   ```
   1. Go to List Settings → Permissions for this list
   2. Click "Stop Inheriting Permissions"
   3. Remove unnecessary groups
   4. Add required groups with appropriate permissions
   ```

2. **Configure List Permissions**
   ```
   Course Advisors:
   - Permission Level: Contribute
   - Advanced Settings: Read items that were created by the user
   
   Data Managers:
   - Permission Level: Contribute
   - Advanced Settings: Read all items
   
   System Administrators:
   - Permission Level: Full Control
   
   Read-Only Users:
   - Permission Level: Read
   ```

### Row-Level Security Implementation

**Method 1: Personal Views (Recommended)**
```
View Configuration:
- Filter: Supervisor equals [Me]
- This automatically shows only records where the current user is the supervisor
- No additional permissions needed
- Works with standard SharePoint authentication
```

**Method 2: Item-Level Permissions (Advanced)**
```
For each student record:
1. Break permission inheritance
2. Grant access only to:
   - The assigned supervisor
   - Data managers
   - System administrators
3. Remove all other users
```

**Implementation Script (PowerShell):**
```powershell
# PowerShell script for item-level permissions
Connect-PnPOnline -Url "https://yourtenant.sharepoint.com/sites/courseadvisor"

$list = Get-PnPList -Identity "Course Approval Pending"
$items = Get-PnPListItem -List $list

foreach ($item in $items) {
    $supervisorEmail = $item["Supervisor"].Email
    
    # Break inheritance
    Set-PnPListItemPermission -List $list -Identity $item.Id -InheritPermissions $false
    
    # Grant access to supervisor
    Set-PnPListItemPermission -List $list -Identity $item.Id -User $supervisorEmail -AddRole "Contribute"
    
    # Grant access to data managers group
    Set-PnPListItemPermission -List $list -Identity $item.Id -Group "Data Managers" -AddRole "Contribute"
    
    # Grant access to system administrators
    Set-PnPListItemPermission -List $list -Identity $item.Id -Group "System Administrators" -AddRole "Full Control"
}
```

## 🔐 Authentication Configuration

### Azure AD Integration

**Single Sign-On Setup:**
1. **Verify Azure AD Connection**
   - SharePoint automatically integrates with Azure AD
   - Users authenticate with institutional credentials
   - No additional configuration needed

2. **Multi-Factor Authentication**
   - Enable MFA for all users accessing sensitive data
   - Configure conditional access policies
   - Require MFA for external access

3. **Guest User Access (if needed)**
   ```
   Configuration:
   - Allow guest users: No (recommended)
   - External sharing: Disabled
   - Anonymous access: Disabled
   ```

### Service Account Configuration

**Power Automate Service Account:**
```
Account: <EMAIL>
Purpose: Automated data synchronization
Permissions:
- SharePoint: Contribute to Course Approval list
- PeopleSoft: Read access to required tables
- File System: Read/Write to export folders
Security:
- Strong password (32+ characters)
- No interactive login allowed
- Regular password rotation (90 days)
- Audit all activities
```

## 📊 Data Classification and Compliance

### Data Sensitivity Levels

| Data Type | Classification | Protection Level |
|-----------|----------------|------------------|
| Student EMPLID | Confidential | High |
| Student Names | Confidential | High |
| Email Addresses | Confidential | High |
| Course Information | Internal | Medium |
| Programme Data | Internal | Medium |
| Status Information | Internal | Medium |

### FERPA Compliance

**Educational Records Protection:**
1. **Access Controls**
   - Only authorized personnel can access student data
   - Role-based access with minimum necessary permissions
   - Regular access reviews and audits

2. **Data Handling**
   - Secure transmission (HTTPS/TLS)
   - Encrypted storage
   - No unauthorized disclosure
   - Proper data retention policies

3. **Audit Requirements**
   - Log all data access
   - Track modifications and deletions
   - Maintain audit trail for 7 years
   - Regular compliance reviews

### Privacy Protection

**Personal Information Handling:**
```
Data Minimization:
- Collect only necessary information
- Limit data retention to business requirements
- Regular data cleanup and archival

Access Logging:
- Log all user access to student data
- Monitor for unusual access patterns
- Alert on unauthorized access attempts

Data Sharing:
- No external data sharing without consent
- Internal sharing limited to business need
- Secure methods for any data transfer
```

## 🔍 Monitoring and Auditing

### Access Monitoring

**SharePoint Audit Configuration:**
1. **Enable Audit Logging**
   ```
   Site Settings → Site collection audit settings
   Enable:
   - Opening or downloading documents
   - Viewing items in lists
   - Editing items
   - Deleting or restoring items
   - Editing content types and columns
   - Searching site content
   ```

2. **Audit Log Review**
   ```
   Weekly Reviews:
   - Check for unauthorized access attempts
   - Review bulk data downloads
   - Monitor administrative changes
   - Validate user access patterns
   ```

### Security Monitoring

**Automated Alerts:**
```
Configure alerts for:
- Failed login attempts (>5 in 1 hour)
- Bulk data downloads (>50 records)
- Administrative permission changes
- External access attempts
- Unusual access patterns
```

**Monthly Security Review:**
```
Review Items:
- User access permissions
- Group memberships
- Service account activities
- Data access patterns
- Security incident reports
```

## 🚨 Incident Response

### Security Incident Types

| Incident Type | Severity | Response Time | Actions |
|---------------|----------|---------------|---------|
| Unauthorized Access | High | 1 hour | Disable account, investigate, notify |
| Data Breach | Critical | 30 minutes | Isolate system, assess impact, notify authorities |
| Permission Escalation | Medium | 4 hours | Review permissions, investigate cause |
| Suspicious Activity | Low | 24 hours | Monitor, investigate, document |

### Incident Response Procedures

**Step 1: Detection and Assessment**
1. Identify the security incident
2. Assess the scope and impact
3. Classify the severity level
4. Notify the incident response team

**Step 2: Containment**
1. Isolate affected systems
2. Disable compromised accounts
3. Preserve evidence
4. Prevent further damage

**Step 3: Investigation**
1. Analyze audit logs
2. Interview affected users
3. Determine root cause
4. Document findings

**Step 4: Recovery**
1. Restore normal operations
2. Apply security patches
3. Update permissions as needed
4. Monitor for recurrence

**Step 5: Lessons Learned**
1. Review incident response
2. Update security procedures
3. Provide additional training
4. Implement preventive measures

## ✅ Security Checklist

### Initial Setup
- [ ] Configure Azure AD authentication
- [ ] Create security groups with appropriate permissions
- [ ] Set up list-level permissions
- [ ] Configure personal views for row-level security
- [ ] Enable audit logging
- [ ] Set up monitoring alerts

### Ongoing Maintenance
- [ ] Weekly audit log review
- [ ] Monthly permission review
- [ ] Quarterly security assessment
- [ ] Annual compliance review
- [ ] Regular user training updates

### Compliance Verification
- [ ] FERPA compliance documented
- [ ] Data classification implemented
- [ ] Audit trail maintained
- [ ] Incident response procedures tested
- [ ] Privacy protection measures verified

## 📞 Security Contacts

### Security Team
- **Information Security Officer**: [Contact Information]
- **Privacy Officer**: [Contact Information]
- **IT Security Team**: [Contact Information]

### Incident Response
- **24/7 Security Hotline**: [Phone Number]
- **Email**: <EMAIL>
- **Emergency Contact**: [Contact Information]

### Compliance
- **Compliance Officer**: [Contact Information]
- **Legal Counsel**: [Contact Information]
- **Audit Team**: [Contact Information]

---

**Document Version**: 1.0  
**Last Updated**: June 19, 2025  
**Next Review**: July 19, 2025
