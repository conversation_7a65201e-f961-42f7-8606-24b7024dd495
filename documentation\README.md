# Course Advisor Automation System

## 📋 Project Overview

This project automates the course approval workflow for Lincoln University, replacing manual email distribution and Excel file management with an integrated SharePoint solution connected to Oracle PeopleSoft.

### Current State
- **Manual Process**: Emailing individual student lists to 30+ course advisors
- **File Management**: Separate Excel files for each advisor
- **Data Source**: Oracle PeopleSoft ERP system
- **Pain Points**: Version control issues, time-consuming updates, no real-time visibility

### Target State
- **Automated Process**: SharePoint list with personal views for each advisor
- **Centralized Data**: Single source of truth with automatic PeopleSoft synchronization
- **Real-time Access**: Advisors see current data instantly via web/mobile
- **Secure Access**: Row-level security using institutional authentication

## 🏗️ Solution Architecture

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PeopleSoft    │───▶│ Power        │───▶│   SharePoint    │───▶│  Course         │
│   ERP System    │    │ Automate     │    │   List          │    │  Advisors       │
│                 │    │ (Daily Sync) │    │ (Personal Views)│    │ (30 users)      │
└─────────────────┘    └──────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Components
1. **Oracle PeopleSoft**: Source system for student and course data
2. **Power Automate**: Automated data synchronization service
3. **SharePoint List**: Central data repository with security
4. **Personal Views**: Filtered views showing only relevant students per advisor

## 📊 Data Structure

### Source Data (PeopleSoft)
- **379 student records** requiring course approval
- **43 unique course advisors/supervisors**
- **Key fields**: Student info, programmes, courses, approval status, advisor assignment

### SharePoint List Schema
| Column | Type | Description |
|--------|------|-------------|
| Student_ID | Number | PeopleSoft EMPLID |
| First_Name | Text | Student first name |
| Last_Name | Text | Student last name |
| Programme | Text | Academic programme |
| Supervisor | Person/Group | Course advisor (filtered by email) |
| Courses_Selected | Multi-line Text | List of courses requiring approval |
| Item_Status | Choice | Pending/Approved/Rejected |
| Status_Date | Date/Time | Last status update |
| Intake | Text | Academic intake period |
| Citizenship | Text | Student citizenship status |
| Acad_Career | Text | Academic career level |

## 🎯 Benefits

### For Course Advisors
- ✅ **Instant Access**: View assigned students anytime, anywhere
- ✅ **Mobile Friendly**: Access from phone/tablet
- ✅ **Search & Filter**: Find specific students or courses quickly
- ✅ **Real-time Data**: Always current information from PeopleSoft
- ✅ **No Training**: Familiar SharePoint interface

### For Administration
- ✅ **Automated Workflow**: Eliminate manual email distribution
- ✅ **Centralized Management**: Single point of data control
- ✅ **Audit Trail**: Track all access and changes
- ✅ **Scalable**: Easy to add more advisors or data fields
- ✅ **Cost Effective**: Uses existing Microsoft infrastructure

### For IT Department
- ✅ **Standard Integration**: Uses proven PeopleSoft export methods
- ✅ **Secure**: Leverages existing authentication and permissions
- ✅ **Maintainable**: Simple architecture with clear data flow
- ✅ **Monitored**: Built-in error handling and notifications

## 📅 Implementation Timeline

### Phase 1: Planning & Design (Week 1)
- [ ] Stakeholder alignment and approval
- [ ] Technical requirements gathering
- [ ] PeopleSoft data mapping
- [ ] SharePoint site planning

### Phase 2: PeopleSoft Integration (Week 2)
- [ ] Create PeopleSoft query for course approval data
- [ ] Set up automated export schedule
- [ ] Configure file sharing location
- [ ] Test data export format

### Phase 3: SharePoint Setup (Week 3)
- [ ] Create SharePoint list structure
- [ ] Configure columns and data types
- [ ] Set up personal views and filters
- [ ] Configure permissions and security

### Phase 4: Power Automate Development (Week 4)
- [ ] Create automated sync flow
- [ ] Configure error handling
- [ ] Test data synchronization
- [ ] Set up monitoring and alerts

### Phase 5: Testing & Deployment (Week 5)
- [ ] Pilot with 3-5 course advisors
- [ ] User acceptance testing
- [ ] Performance optimization
- [ ] Full deployment to all advisors

### Phase 6: Training & Support (Week 6)
- [ ] Create user documentation
- [ ] Conduct advisor training sessions
- [ ] Establish support procedures
- [ ] Monitor system performance

## 👥 Stakeholders & Responsibilities

### Project Sponsor
- **Role**: Executive approval and resource allocation
- **Responsibilities**: Project authorization, budget approval, change management

### IT Department
- **Role**: Technical implementation and support
- **Responsibilities**: PeopleSoft configuration, infrastructure setup, security

### Course Advisors (30 users)
- **Role**: End users of the system
- **Responsibilities**: User acceptance testing, feedback, adoption

### Project Manager
- **Role**: Implementation coordination
- **Responsibilities**: Timeline management, stakeholder communication, documentation

## 📋 Success Criteria

### Functional Requirements
- [ ] Each advisor sees only their assigned students
- [ ] Data synchronizes daily from PeopleSoft
- [ ] Search and filter functionality works correctly
- [ ] Mobile access is fully functional
- [ ] System handles 379+ student records efficiently

### Performance Requirements
- [ ] Page load time < 3 seconds
- [ ] Data sync completes within 30 minutes
- [ ] 99% uptime during business hours
- [ ] Support for 30 concurrent users

### User Adoption Requirements
- [ ] 90% of advisors actively using system within 2 weeks
- [ ] Reduction in support tickets related to student lists
- [ ] Positive user feedback scores (>4/5)

## 🔗 Documentation Index

### Technical Documentation
- [PeopleSoft Integration Guide](./01_PeopleSoft_Integration.md)
- [SharePoint Configuration Guide](./02_SharePoint_Configuration.md)
- [Power Automate Setup Guide](./03_Power_Automate_Setup.md)
- [Security & Permissions Guide](./04_Security_Permissions.md)

### User Documentation
- [Course Advisor User Guide](./05_User_Guide.md)
- [Administrator Manual](./06_Administrator_Manual.md)
- [Troubleshooting Guide](./07_Troubleshooting.md)

### Project Management
- [Implementation Checklist](./08_Implementation_Checklist.md)
- [Testing Plan](./09_Testing_Plan.md)
- [Training Materials](./10_Training_Materials.md)

## 🚀 Quick Start

### For Project Managers
1. Review [Implementation Checklist](./08_Implementation_Checklist.md)
2. Coordinate with IT for PeopleSoft access
3. Set up project timeline and milestones

### For IT Teams
1. Start with [PeopleSoft Integration Guide](./01_PeopleSoft_Integration.md)
2. Follow [SharePoint Configuration Guide](./02_SharePoint_Configuration.md)
3. Implement [Power Automate Setup Guide](./03_Power_Automate_Setup.md)

### For Course Advisors
1. Review [Course Advisor User Guide](./05_User_Guide.md)
2. Participate in user acceptance testing
3. Provide feedback for system improvements

## 📞 Support & Contact

### Technical Support
- **IT Helpdesk**: [Contact Information]
- **SharePoint Admin**: [Contact Information]
- **PeopleSoft Team**: [Contact Information]

### Project Team
- **Project Manager**: [Your Name and Contact]
- **Technical Lead**: [Contact Information]
- **Business Analyst**: [Contact Information]

---

**Document Version**: 1.0  
**Last Updated**: June 19, 2025  
**Next Review**: July 19, 2025
