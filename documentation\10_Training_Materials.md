# Training Materials

## 📋 Overview

This document provides comprehensive training materials for the Course Advisor Automation System, including training plans, presentation materials, and hands-on exercises.

## 🎯 Training Objectives

### Primary Learning Goals
- Understand the new course approval system
- Navigate the SharePoint interface effectively
- Use search and filtering features efficiently
- Access the system from mobile devices
- Know where to get help when needed

### Target Audiences
- **Course Advisors** (30 users) - Primary system users
- **Administrative Staff** (5 users) - Data managers and support
- **IT Support** (3 users) - Technical support and maintenance

## 📚 Training Program Structure

### Training Delivery Methods

**Option 1: In-Person Sessions**
- Duration: 45 minutes per session
- Group size: 8-10 participants
- Interactive demonstrations
- Hands-on practice time
- Q&A session

**Option 2: Virtual Sessions**
- Duration: 30 minutes presentation + 15 minutes Q&A
- Screen sharing demonstrations
- Recorded for future reference
- Follow-up support available

**Option 3: Self-Paced Learning**
- Online training materials
- Step-by-step guides
- Video tutorials
- Practice exercises

## 🎓 Course Advisor Training

### Session 1: System Introduction (15 minutes)

**Learning Objectives:**
- Understand why the new system was implemented
- Learn the benefits of the automated approach
- Overview of system capabilities

**Presentation Outline:**
```
1. Welcome and Introductions (2 minutes)
2. Current Process Challenges (3 minutes)
   - Manual email distribution
   - Excel file management issues
   - Version control problems
3. New System Benefits (5 minutes)
   - Real-time access to student data
   - Mobile accessibility
   - Automatic filtering by advisor
   - Search and filter capabilities
4. System Overview (5 minutes)
   - SharePoint-based solution
   - Daily updates from PeopleSoft
   - Personal views for each advisor
```

### Session 2: Hands-On Navigation (20 minutes)

**Learning Objectives:**
- Access the system successfully
- Navigate the main interface
- Understand the information displayed

**Demonstration Script:**
```
1. System Access (5 minutes)
   "Let's start by accessing the system..."
   - Open browser and navigate to SharePoint site
   - Sign in with institutional credentials
   - Locate "Course Approval Pending" list
   
2. Interface Overview (10 minutes)
   "Now let's explore what you see on screen..."
   - Point out main navigation elements
   - Explain the student list layout
   - Show information displayed for each student
   - Demonstrate the view selector
   
3. Understanding Your Data (5 minutes)
   "The system automatically shows only your students..."
   - Explain automatic filtering
   - Show different status types
   - Discuss data freshness and updates
```

**Hands-On Exercise 1:**
```
Exercise: Find Your Students
Time: 5 minutes
Instructions:
1. Access the Course Approval Pending list
2. Count how many pending students you have
3. Find a student with the last name starting with "S"
4. Note what programme they're enrolled in
```

### Session 3: Search and Filter Features (15 minutes)

**Learning Objectives:**
- Use the search functionality effectively
- Switch between different views
- Apply filters to find specific information

**Demonstration Script:**
```
1. Search Functionality (7 minutes)
   "The search box helps you find specific students..."
   - Demonstrate searching by student name
   - Show searching by programme
   - Try searching by course code
   - Explain partial matching
   
2. View Options (5 minutes)
   "Different views show different information..."
   - Switch to "My Pending Students"
   - Show "My All Students" view
   - Demonstrate "Recently Updated" view
   
3. Sorting and Filtering (3 minutes)
   "You can organize the information..."
   - Click column headers to sort
   - Show reverse sorting
   - Explain default sorting logic
```

**Hands-On Exercise 2:**
```
Exercise: Search and Filter Practice
Time: 10 minutes
Instructions:
1. Search for a student by first name
2. Switch to "My All Students" view
3. Sort the list by Status Date (newest first)
4. Find all students in "Business Management" programme
5. Count how many students have "Approved" status
```

### Session 4: Mobile Access (10 minutes)

**Learning Objectives:**
- Access the system from mobile devices
- Navigate the mobile interface
- Understand mobile-specific features

**Demonstration Script:**
```
1. Mobile Access (5 minutes)
   "You can access the system from anywhere..."
   - Show accessing SharePoint on mobile
   - Demonstrate responsive design
   - Point out touch-friendly interface
   
2. Mobile Navigation (5 minutes)
   "The mobile interface adapts to your device..."
   - Show scrolling and navigation
   - Demonstrate search on mobile
   - Explain landscape vs. portrait mode
```

**Hands-On Exercise 3:**
```
Exercise: Mobile Practice (Optional)
Time: 5 minutes
Instructions:
1. Access the system on your mobile device
2. Find your pending students
3. Search for a specific student
4. Try both portrait and landscape modes
```

### Session 5: Getting Help and Wrap-Up (10 minutes)

**Learning Objectives:**
- Know where to get help
- Understand support procedures
- Provide feedback on the training

**Content:**
```
1. Support Resources (5 minutes)
   - IT Helpdesk contact information
   - User guide location
   - Common troubleshooting tips
   
2. Best Practices (3 minutes)
   - Check the system daily
   - Use specific search terms
   - Refresh for latest data
   
3. Q&A and Feedback (2 minutes)
   - Answer remaining questions
   - Collect feedback on training
   - Provide next steps
```

## 🎥 Video Tutorial Scripts

### Video 1: "Getting Started" (3 minutes)

**Script:**
```
[INTRO - 15 seconds]
"Welcome to the Course Advisor Automation System. I'm going to show you how to access and use this new system to view your assigned students."

[ACCESSING THE SYSTEM - 45 seconds]
"First, open your web browser and navigate to [SharePoint URL]. Sign in with your usual institutional email and password. Once you're logged in, click on 'Course Approval Pending' in the left navigation menu."

[UNDERSTANDING THE INTERFACE - 90 seconds]
"You'll see a list of students assigned to you. Each entry shows the student's name, their programme, the courses they've selected, and the current approval status. The system automatically filters to show only your students - you won't see students assigned to other advisors."

[BASIC NAVIGATION - 30 seconds]
"You can scroll through the list to see all your students. The most recent submissions appear at the top. Notice the search box at the top - we'll cover that in the next video."

[CLOSING - 15 seconds]
"That's the basic overview. In the next video, we'll learn how to search for specific students and use the different views available."
```

### Video 2: "Search and Filter" (4 minutes)

**Script:**
```
[INTRO - 15 seconds]
"In this video, I'll show you how to search for specific students and use the different view options to organize your information."

[SEARCH FUNCTIONALITY - 2 minutes]
"The search box at the top lets you find students quickly. You can search by the student's first or last name, their programme, or even course codes. Let me demonstrate..."
[Show typing "John" and results filtering]
"Notice how the list immediately filters to show only students with 'John' in their name. You can also search for programmes..."
[Show typing "Business" and results]
"Or course codes..."
[Show typing "MGMT" and results]

[VIEW OPTIONS - 90 seconds]
"The system provides different views to help you organize your work. The default view shows 'My Pending Students' - these are students waiting for your approval. You can switch views using the dropdown..."
[Show switching to "My All Students"]
"This view shows all your students regardless of status. The 'Recently Updated' view shows students whose information has changed in the last week."

[SORTING - 30 seconds]
"You can sort the information by clicking on column headers. Click once to sort ascending, click again for descending order."

[CLOSING - 15 seconds]
"These search and filter features will help you find information quickly. Practice using them to become more efficient."
```

### Video 3: "Mobile Access" (2 minutes)

**Script:**
```
[INTRO - 15 seconds]
"This video shows how to access the Course Advisor system from your mobile phone or tablet."

[MOBILE ACCESS - 60 seconds]
"Open your mobile browser and go to the same SharePoint URL. The interface automatically adapts to your screen size. Sign in with your credentials just like on desktop."
[Show mobile interface]
"The mobile version includes all the same functionality - you can view your students, search, and switch between views."

[MOBILE TIPS - 30 seconds]
"For better viewing, try rotating your device to landscape mode. You can pinch to zoom if text appears too small. Pull down on the list to refresh the data."

[CLOSING - 15 seconds]
"Mobile access means you can check your student approvals from anywhere. This is especially useful when you're away from your office."
```

## 📖 Quick Reference Guides

### One-Page Quick Start Guide

```
┌─────────────────────────────────────────────────────────────────┐
│                    COURSE ADVISOR QUICK START                  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ 🌐 ACCESS: [SharePoint URL]                                    │
│ 🔑 LOGIN: Your institutional email and password                │
│                                                                 │
│ 📋 MAIN FEATURES:                                              │
│ • View only YOUR assigned students                             │
│ • Search by name, programme, or course                         │
│ • Switch between Pending/All/Recent views                      │
│ • Access from mobile devices                                   │
│                                                                 │
│ 🔍 SEARCH TIPS:                                                │
│ • Type student name: "John Smith"                             │
│ • Type programme: "Business"                                   │
│ • Type course: "MGMT"                                         │
│                                                                 │
│ 📱 MOBILE: Same URL, works on phones and tablets              │
│                                                                 │
│ 🆘 HELP: IT Helpdesk [Phone] or [Email]                       │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### Troubleshooting Quick Reference

```
┌─────────────────────────────────────────────────────────────────┐
│                    COMMON ISSUES & SOLUTIONS                   │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ ❌ PROBLEM: Can't see any students                             │
│ ✅ SOLUTION: Check if you're in the right view, try "My All   │
│    Students" view                                              │
│                                                                 │
│ ❌ PROBLEM: Page loading slowly                                │
│ ✅ SOLUTION: Refresh the page (F5) or try a different browser │
│                                                                 │
│ ❌ PROBLEM: Search not working                                 │
│ ✅ SOLUTION: Clear the search box and try different terms     │
│                                                                 │
│ ❌ PROBLEM: Can't find a specific student                     │
│ ✅ SOLUTION: Try searching by last name only, check spelling  │
│                                                                 │
│ ❌ PROBLEM: Mobile view looks wrong                            │
│ ✅ SOLUTION: Try landscape mode, refresh the page             │
│                                                                 │
│ 🆘 STILL NEED HELP? Contact IT Helpdesk: [Phone/Email]        │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 📊 Training Assessment

### Knowledge Check Quiz

**Question 1:** How do you access the Course Advisor system?
a) Through email links
b) Via SharePoint using your institutional login
c) Through a special application
d) By calling IT support

**Question 2:** What students will you see in the system?
a) All students in the university
b) Only students in your department
c) Only students assigned to you as their advisor
d) Students you manually add

**Question 3:** How often is the data updated?
a) Real-time
b) Every hour
c) Daily
d) Weekly

**Question 4:** What can you search for in the system?
a) Student names only
b) Student names and programmes
c) Student names, programmes, and courses
d) Only course codes

**Question 5:** Can you access the system from your mobile phone?
a) No, desktop only
b) Yes, with full functionality
c) Yes, but with limited features
d) Only with a special app

**Answers:** 1-b, 2-c, 3-c, 4-c, 5-b

### Practical Skills Assessment

**Task 1:** Access the system and count your pending students
**Task 2:** Search for a student by name
**Task 3:** Switch to "My All Students" view
**Task 4:** Sort the list by Status Date
**Task 5:** Access the system on a mobile device

## 📅 Training Schedule Template

### Week 1: Course Advisor Training

| Day | Time | Session | Attendees | Location |
|-----|------|---------|-----------|----------|
| Monday | 10:00-10:45 | Group 1 (8 advisors) | [Names] | Conference Room A |
| Monday | 2:00-2:45 | Group 2 (8 advisors) | [Names] | Conference Room A |
| Tuesday | 10:00-10:45 | Group 3 (8 advisors) | [Names] | Conference Room A |
| Tuesday | 2:00-2:45 | Group 4 (6 advisors) | [Names] | Conference Room A |
| Wednesday | 10:00-10:30 | Virtual Session | All advisors | Online |
| Thursday | 2:00-3:00 | Q&A Session | Open to all | Conference Room A |
| Friday | All day | Self-paced practice | Individual | Own workspace |

### Week 2: Support and Follow-up

| Day | Activity | Duration | Responsibility |
|-----|----------|----------|----------------|
| Monday | Check-in calls | 15 min each | Training coordinator |
| Tuesday | Additional support | As needed | IT helpdesk |
| Wednesday | Feedback collection | 30 minutes | Project manager |
| Thursday | Issue resolution | As needed | Technical team |
| Friday | Training effectiveness review | 1 hour | Project team |

## 📞 Training Support Contacts

### Training Team
- **Training Coordinator**: [Name, Email, Phone]
- **Technical Trainer**: [Name, Email, Phone]
- **User Support**: [Name, Email, Phone]

### Subject Matter Experts
- **SharePoint Expert**: [Name, Email, Phone]
- **Course Advisor Representative**: [Name, Email, Phone]
- **IT Support Lead**: [Name, Email, Phone]

---

**Document Version**: 1.0  
**Last Updated**: June 19, 2025  
**Next Review**: July 19, 2025
